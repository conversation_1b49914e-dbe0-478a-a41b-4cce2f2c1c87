<template>
  <div class="app-container" v-loading="mainLoading">
    <!-- 顶部操作区域 -->
    <div class="header-section">
      <div class="header-left">
        <el-form :model="searchForm" inline>
          <el-form-item label="销售单号：">
            <el-input
              ref="erpOrderCode"
              v-model="searchForm.erpOrderCode"
              placeholder="请输入销售单号或扫码"
              style="width: 300px;"
              @keyup.enter.native="searchOrder"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchOrder">查看任务</el-button>
            <el-button type="success" @click="refreshPage">刷新</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <div class="status-info">
          <span class="label">快递类型：</span>
          <span class="value">{{ orderInfo.expressType || '顺丰(易破损)' }}</span>
        </div>
        <div class="status-info">
          <span class="label">订单类型：</span>
          <span class="value">{{ orderInfo.orderType || '京东' }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧商品图片区域 -->
      <div class="left-section">
        <div class="product-image-container">
          <div class="image-placeholder">
            <el-image
              :src="currentProduct.productImage"
              fit="contain"
              style="width: 100%; height: 100%;"
            >
              <div slot="placeholder" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <div>商品图片</div>
              </div>
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <div>商品图片</div>
              </div>
            </el-image>
          </div>
        </div>

        <!-- 商品基本信息 -->
        <div class="product-basic-info">
          <div class="info-row">
            <span class="label">商品条码：</span>
            <span class="value">{{ currentProduct.productCode || '69010193485576' }}</span>
          </div>
          <div class="info-row">
            <span class="label">商品名称：</span>
            <span class="value">{{ currentProduct.productName || '三九感冒灵颗粒 10袋/盒' }}</span>
          </div>
          <div class="info-row">
            <span class="label">商品批号：</span>
            <span class="value">{{ currentProduct.batchNumber || 'Y1003020' }}</span>
          </div>
          <div class="info-row">
            <span class="label">包装单位：</span>
            <span class="value">{{ currentProduct.packingUnit || '盒' }}</span>
          </div>
        </div>
      </div>

      <!-- 中间商品列表区域 -->
      <div class="center-section">
        <div class="product-table-container">
          <vxe-table
            ref="productTable"
            :data="productList"
            height="400"
            stripe
            border
            highlight-hover-row
            highlight-current-row
            @current-change="handleCurrentChange"
          >
            <vxe-table-column field="productCode" title="商品编码" width="120" />
            <vxe-table-column field="productName" title="商品名称" width="150" />
            <vxe-table-column field="batchNumber" title="商品批号" width="120" />
            <vxe-table-column field="productCodeDisplay" title="商品编码(双位显示)" width="150" />
            <vxe-table-column field="packedQuantity" title="已装数" width="80" />
            <vxe-table-column field="targetQuantity" title="本装数" width="80" />
            <vxe-table-column field="totalQuantity" title="总装数" width="80" />
          </vxe-table>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-button type="primary" @click="confirmPacking">复核确认</el-button>
          <el-button type="danger" @click="submitException">异常提交</el-button>
          <el-button type="warning" @click="updateCarrier">更换承运商</el-button>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="right-section">
        <div class="operation-form">
          <el-form :model="operationForm" label-width="100px">
            <el-form-item label="耗材确认：">
              <el-input
                ref="materialCode"
                v-model="operationForm.materialCode"
                placeholder="H001"
                @keyup.enter.native="confirmMaterial"
              />
            </el-form-item>
            <el-form-item label="耗材名称：">
              <el-input
                v-model="operationForm.materialName"
                placeholder="二手箱"
                readonly
              />
            </el-form-item>
            <el-form-item label="操作：">
              <el-input
                v-model="operationForm.operation"
                placeholder="确认"
                readonly
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 底部统计信息区域 -->
    <div class="bottom-section">
      <div class="statistics-info">
        <div class="stat-item">
          <span class="label">已装箱商品总数</span>
          <span class="value red-text">{{ statistics.packedTotal || 10 }}</span>
        </div>
        <div class="stat-item">
          <span class="label">未装箱商品总数</span>
          <span class="value red-text">{{ statistics.unpackedTotal || 250 }}</span>
        </div>
        <div class="stat-item">
          <span class="label">商品总数</span>
          <span class="value red-text">{{ statistics.grandTotal || 260 }}</span>
        </div>
      </div>

      <!-- 底部表格 -->
      <div class="bottom-table">
        <vxe-table
          ref="bottomTable"
          :data="bottomTableData"
          height="150"
          stripe
          border
        >
          <vxe-table-column field="productCode" title="商品条码" width="120" />
          <vxe-table-column field="queryName" title="查询名称" width="120" />
          <vxe-table-column field="packedQuantity" title="已装箱数量" width="120" />
          <vxe-table-column field="unpackedQuantity" title="未装箱数量" width="120" />
          <vxe-table-column field="productBarcode" title="商品条码" width="120" />
          <vxe-table-column field="productCodeBottom" title="商品编码" width="120" />
        </vxe-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getPartsInReviewGoodsSalesInfo,
  listPartsInReviewGoodsView,
  review,
  reviewConfirm,
  checkParts
} from "@/api/outstock/fhdb";

export default {
  name: "NewReviewPackage",
  data() {
    return {
      mainLoading: false,
      // 搜索表单
      searchForm: {
        erpOrderCode: ""
      },
      // 订单信息
      orderInfo: {
        expressType: "顺丰(易破损)",
        orderType: "京东"
      },
      // 当前选中商品信息
      currentProduct: {
        productCode: "69010193485576",
        productName: "三九感冒灵颗粒 10袋/盒",
        batchNumber: "Y1003020",
        packingUnit: "盒",
        productImage: ""
      },
      // 商品列表数据
      productList: [
        {
          productCode: "69010193485576",
          productName: "三九感冒灵颗粒 10袋/盒",
          batchNumber: "250501(20)",
          productCodeDisplay: "Y1003020",
          packedQuantity: 0,
          targetQuantity: 20,
          totalQuantity: 20
        }
      ],
      // 操作表单
      operationForm: {
        materialCode: "",
        materialName: "二手箱",
        operation: "确认"
      },
      // 统计信息
      statistics: {
        packedTotal: 10,
        unpackedTotal: 250,
        grandTotal: 260
      },
      // 底部表格数据
      bottomTableData: [],
      // 业务数据
      mergeOrderCode: "",
      orderCode: "",
      allocationCode: ""
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.erpOrderCode.focus();
    });
  },
  methods: {
    // 搜索订单
    searchOrder() {
      if (!this.searchForm.erpOrderCode.trim()) {
        this.$message.warning("请输入销售单号");
        return;
      }
      this.getOrderInfo();
    },

    // 刷新页面
    refreshPage() {
      this.clearPageData();
      this.$nextTick(() => {
        this.$refs.erpOrderCode.focus();
      });
    },

    // 清空页面数据
    clearPageData() {
      this.searchForm.erpOrderCode = "";
      this.currentProduct = {
        productCode: "",
        productName: "",
        batchNumber: "",
        packingUnit: "",
        productImage: ""
      };
      this.productList = [];
      this.operationForm.materialCode = "";
      this.bottomTableData = [];
      this.statistics = {
        packedTotal: 0,
        unpackedTotal: 0,
        grandTotal: 0
      };
    },

    // 获取订单信息
    getOrderInfo() {
      this.mainLoading = true;
      getPartsInReviewGoodsSalesInfo({
        erpOrderCode: this.searchForm.erpOrderCode
      }).then((res) => {
        this.mainLoading = false;
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.mergeOrderCode = result.mergeOrderCode;
          this.orderCode = result.orderCode;
          this.allocationCode = result.allocationCode;
          this.orderInfo.expressType = result.expressType || "顺丰(易破损)";
          this.orderInfo.orderType = result.orderType || "京东";
          this.loadProductList();
          this.$message.success("订单信息加载成功");
        } else {
          this.$message.error(msg || "获取订单信息失败");
        }
      }).catch(() => {
        this.mainLoading = false;
        this.$message.error("网络错误，请重试");
      });
    },

    // 加载商品列表
    loadProductList() {
      listPartsInReviewGoodsView({
        orderCode: this.orderCode,
        allocationCode: this.allocationCode,
        mergeOrderCode: this.mergeOrderCode
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.productList = result.map(item => ({
            productCode: item.productCode,
            productName: item.productName,
            batchNumber: item.batchNumber,
            productCodeDisplay: item.productCodeDisplay || item.productCode,
            packedQuantity: item.packedQuantity || 0,
            targetQuantity: item.reviewNumber || 0,
            totalQuantity: item.totalQuantity || item.reviewNumber || 0,
            productImage: item.productPic && item.productPic[0] ? item.productPic[0] : ""
          }));

          // 设置默认选中第一个商品
          if (this.productList.length > 0) {
            this.handleCurrentChange({ row: this.productList[0] });
          }

          this.updateStatistics();
        } else {
          this.$message.error(msg || "获取商品列表失败");
        }
      });
    },

    // 表格行选中事件
    handleCurrentChange({ row }) {
      if (row) {
        this.currentProduct = {
          productCode: row.productCode,
          productName: row.productName,
          batchNumber: row.batchNumber,
          packingUnit: row.packingUnit || "盒",
          productImage: row.productImage
        };
      }
    },

    // 确认耗材
    confirmMaterial() {
      if (!this.operationForm.materialCode.trim()) {
        this.$message.warning("请输入耗材码");
        return;
      }

      checkParts({
        boxCode: this.operationForm.materialCode
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.operationForm.materialName = result.boxType || "二手箱";
          this.$message.success("耗材确认成功");
          this.operationForm.materialCode = "";
        } else {
          this.$message.error(msg || "耗材确认失败");
        }
      });
    },

    // 复核确认
    confirmPacking() {
      if (!this.mergeOrderCode) {
        this.$message.warning("请先搜索订单");
        return;
      }

      this.mainLoading = true;
      reviewConfirm({
        orderCode: this.orderCode,
        mergeOrderCode: this.mergeOrderCode
      }).then((res) => {
        this.mainLoading = false;
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success(msg || "复核确认成功");
          this.clearPageData();
        } else {
          this.$message.error(msg || "复核确认失败");
        }
      }).catch(() => {
        this.mainLoading = false;
        this.$message.error("网络错误，请重试");
      });
    },

    // 异常提交
    submitException() {
      this.$message.info("异常提交功能开发中...");
    },

    // 更换承运商
    updateCarrier() {
      this.$message.info("更换承运商功能开发中...");
    },

    // 更新统计信息
    updateStatistics() {
      let packedTotal = 0;
      let unpackedTotal = 0;

      this.productList.forEach(item => {
        packedTotal += item.packedQuantity || 0;
        unpackedTotal += (item.totalQuantity || 0) - (item.packedQuantity || 0);
      });

      this.statistics = {
        packedTotal,
        unpackedTotal,
        grandTotal: packedTotal + unpackedTotal
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-left {
    flex: 1;
  }

  .header-right {
    display: flex;
    gap: 30px;

    .status-info {
      .label {
        font-weight: bold;
        color: #333;
      }
      .value {
        color: #1890ff;
        font-weight: 500;
      }
    }
  }
}

.main-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.left-section {
  width: 400px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .product-image-container {
    margin-bottom: 20px;

    .image-placeholder {
      width: 100%;
      height: 300px;
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;

      .image-slot {
        text-align: center;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 10px;
        }

        div {
          font-size: 14px;
        }
      }
    }
  }

  .product-basic-info {
    .info-row {
      display: flex;
      margin-bottom: 12px;
      align-items: center;

      .label {
        width: 100px;
        font-weight: bold;
        color: #333;
      }

      .value {
        flex: 1;
        color: #666;
        font-size: 14px;
      }
    }
  }
}

.center-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .product-table-container {
    margin-bottom: 20px;
  }

  .action-buttons {
    text-align: center;

    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
      font-size: 16px;
    }
  }
}

.right-section {
  width: 300px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .operation-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}

.bottom-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .statistics-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .label {
        display: block;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .value {
        display: block;
        font-size: 24px;
        font-weight: bold;

        &.red-text {
          color: #f56c6c;
        }
      }
    }
  }

  .bottom-table {
    .vxe-table {
      border: 1px solid #e4e7ed;
    }
  }
}

// 表格样式优化
::v-deep .vxe-table {
  .vxe-header--column {
    background-color: #f5f7fa;
    font-weight: bold;
  }

  .vxe-body--row:hover {
    background-color: #f5f7fa;
  }

  .vxe-body--row.row--current {
    background-color: #ecf5ff;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .left-section,
  .right-section {
    width: 100%;
  }
}
</style>