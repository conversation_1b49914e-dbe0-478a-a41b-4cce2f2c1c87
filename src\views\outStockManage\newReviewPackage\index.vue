<template>
  <div class="new-review-package-container" v-loading="mainLoading">
    <!-- 顶部操作区域 -->
    <div class="top-section">
      <!-- 销售单号输入区域 -->
      <div class="sales-order-section">
        <div class="input-group">
          <label class="input-label">销售单号</label>
          <el-input
            ref="erpOrderCode"
            v-model="formData.erpOrderCode"
            placeholder="请输入销售单号或扫描条码，或者扫描面单"
            class="sales-order-input"
            onfocus="this.select()"
            @keyup.enter.native="handleSalesOrderEnter"
          />
          <el-button type="primary" @click="handleQueryTask">查看任务</el-button>
          <el-button type="warning" @click="handleRefresh">刷新</el-button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-info">
          <div class="stat-item">
            <span class="stat-label">随货同行数量:</span>
            <span class="stat-value">{{ statisticsData.accompanyingCount || 2 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">未打:</span>
            <span class="stat-value unprinted">{{ statisticsData.unprintedCount || 1 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">快递类型:</span>
            <span class="stat-value">{{ deliveryInfo.type || '顺丰(易碎损)' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">订单类型:</span>
            <span class="stat-value">{{ deliveryInfo.orderType || '京东' }}</span>
          </div>
        </div>
      </div>

      <!-- 耗材码输入区域 -->
      <div class="material-code-section">
        <div class="input-group">
          <label class="input-label">耗材码</label>
          <el-input
            ref="materialCode"
            v-model="formData.materialCode"
            placeholder="请扫描耗材码"
            class="material-code-input"
            onfocus="this.select()"
            @keyup.enter.native="handleMaterialCodeEnter"
          />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧商品图片区域 -->
      <div class="left-section">
        <div class="product-image-container">
          <div class="image-placeholder">
            <el-image
              :src="currentProduct.productImage || require('@/assets/images/product.png')"
              fit="contain"
              class="product-image"
            >
              <div slot="placeholder" class="image-slot">
                <span>商品图片</span>
              </div>
            </el-image>
          </div>
        </div>

        <!-- 商品基本信息 -->
        <div class="product-basic-info">
          <div class="info-row">
            <span class="info-label">商品条码:</span>
            <span class="info-value">{{ currentProduct.productCode || '69010193485576' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">商品名称:</span>
            <span class="info-value">{{ currentProduct.productName || '三九感冒灵颗粒 10袋/盒' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">商品编号:</span>
            <span class="info-value">{{ currentProduct.productNumber || 'Y1003020' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">包装单位:</span>
            <span class="info-value">{{ currentProduct.packingUnit || '盒' }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧商品列表区域 -->
      <div class="right-section">
        <!-- 商品列表表格 -->
        <div class="product-table-container">
          <vxe-table
            ref="productTable"
            :data="productList"
            height="300"
            stripe
            border
            :cell-class-name="getCellClassName"
            @cell-click="handleCellClick"
          >
            <vxe-table-column field="productCode" title="商品条码" width="120" />
            <vxe-table-column field="productName" title="商品名称" width="150" />
            <vxe-table-column field="productNumber" title="商品编号" width="100" />
            <vxe-table-column field="productCodeDisplay" title="商品编码(仅显示后6位)" width="150" />
            <vxe-table-column field="packedCount" title="已装数" width="80" />
            <vxe-table-column field="targetCount" title="未装数" width="80" />
            <vxe-table-column field="totalCount" title="总数数" width="80" />
          </vxe-table>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-button type="primary" @click="handlePackageConfirm">包装确认</el-button>
          <el-button type="danger" @click="handleExceptionSubmit">异常提交</el-button>
          <el-button type="info" @click="handleUpdateTransport">更换运输</el-button>
        </div>

        <!-- 统计信息区域 -->
        <div class="package-stats">
          <div class="stats-row">
            <div class="stat-group">
              <span class="stat-title">已装箱商品总数</span>
              <span class="stat-number">{{ packageStats.packedTotal || 10 }}</span>
            </div>
            <div class="stat-group">
              <span class="stat-title">未装箱商品总数</span>
              <span class="stat-number unpackaged">{{ packageStats.unpackedTotal || 250 }}</span>
            </div>
            <div class="stat-group">
              <span class="stat-title">商品总数</span>
              <span class="stat-number">{{ packageStats.grandTotal || 260 }}</span>
            </div>
          </div>
        </div>

        <!-- 底部详细信息表格 -->
        <div class="detail-table-container">
          <vxe-table
            ref="detailTable"
            :data="detailList"
            height="150"
            stripe
            border
          >
            <vxe-table-column field="productCode" title="商品条码" width="120" />
            <vxe-table-column field="productName" title="商品名称" width="150" />
            <vxe-table-column field="packedQuantity" title="已装箱数量" width="100" />
            <vxe-table-column field="unpackedQuantity" title="未装箱数量" width="100" />
            <vxe-table-column field="productNumber" title="商品编号" width="100" />
            <vxe-table-column field="productGroup" title="商品组别" width="100" />
          </vxe-table>
        </div>

        <!-- 扫描输入区域 -->
        <div class="scan-input-section">
          <div class="scan-input-group">
            <div class="input-item">
              <label class="scan-label">耗材编码</label>
              <el-input
                v-model="scanData.materialCode"
                placeholder="H001"
                class="scan-input"
              />
            </div>
            <div class="input-item">
              <label class="scan-label">耗材名称</label>
              <el-input
                v-model="scanData.materialName"
                placeholder="二手箱"
                class="scan-input"
              />
            </div>
            <div class="input-item">
              <label class="scan-label">操作</label>
              <el-button type="text" @click="handleDeleteOperation">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复用原有的弹窗组件 -->
    <!-- 追溯码扫描弹窗 -->
    <tracing-code-scan
      ref="tracingCodeScanDialog"
      @on-before-close="closeDialog"
      @regulatoryCodeScanBack="handleTracingCodeScanBack"
    />

    <!-- 查看任务弹窗 -->
    <view-tasks
      ref="viewTasksDialog"
      @on-before-close="closeViewTasksDialog"
    />

    <!-- 异常提交弹窗 -->
    <exception-submission
      ref="exceptionSubmitDialog"
      @on-before-close="closeExceptionSubmitDialog"
      @inputOpenDialog="openGoodsDialog"
      @deleteRow="deleteRow"
    />

    <!-- 商品列表弹窗 -->
    <goods-dialog
      ref="goodsDialog"
      @checkGoodsData="checkGoodsData"
    />

    <!-- 承运商更换弹窗 -->
    <changecys
      ref="changecys"
      @back-data="changecysBackData"
    />

    <!-- 承运商更换确认弹窗 -->
    <changeCYSAlertNew
      ref="dialogChangeCYSAlert"
    />

    <!-- 编辑箱码弹窗 -->
    <editBox
      ref="editBox"
    />
  </div>
</template>